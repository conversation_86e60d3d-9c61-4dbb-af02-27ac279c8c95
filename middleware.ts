import { NextRequest, NextResponse } from 'next/server';

export function middleware(req: NextRequest) {
  // SIMPLE TEST - just log and redirect everything to /pending-approval
  console.log(`🚨🚨🚨 MIDDLEWARE RUNNING - ${req.nextUrl.pathname}`);

  // Skip static files
  if (req.nextUrl.pathname.startsWith('/_next/') ||
      req.nextUrl.pathname.startsWith('/api/') ||
      req.nextUrl.pathname.includes('.')) {
    return NextResponse.next();
  }

  // For testing - redirect everything to pending-approval
  if (req.nextUrl.pathname !== '/pending-approval') {
    console.log(`🔄 REDIRECTING ${req.nextUrl.pathname} to /pending-approval`);
    return NextResponse.redirect(new URL('/pending-approval', req.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
